# OPENAI
OPENAI_API_KEY=********************************************************************************************************************************************************************

# FIREBASE
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyCqa9BpCNRO5TTlixOfNM95uUTyitJ25TA
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=brotrip-d7c58.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=brotrip-d7c58
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=brotrip-d7c58.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=************
NEXT_PUBLIC_FIREBASE_APP_ID=1:************:web:003c4500481382417f133c
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-Z5VPL5WYVE

# FIREBASE ADMIN SDK (for server-side authentication)
# Generate this from Firebase Console > Project Settings > Service Accounts > Generate New Private Key
# Then paste the JSON content here as a string (escape quotes if needed)
FIREBASE_SERVICE_ACCOUNT_KEY='********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
'
# WEATHER API
WEATHER_API_KEY=82459ed6fe5048f1b6d145420231604

# GOOGLE PLACES API
GOOGLE_PLACES_API_KEY=AIzaSyCT_KkTufSuZstfXxfaFkcXt8s37hir9k4
# Note: We've removed NEXT_PUBLIC_GOOGLE_PLACES_API_KEY as it should only be used server-side

# STRIPE
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51RFe8VGaMPDCTgYKhKxOm9JgrSkfzA8zPBqKyUyRU7kKvVRJLbNVByJTcoIwptolp9WmXnA04DBT4hYA4FWuBofR00HsS3eUC2
STRIPE_SECRET_KEY=sk_test_51RFe8VGaMPDCTgYK0a5TYfI0Tj3U4g31dPZ31aEai63By3uoqO6bvficjD9YRbCDbNTCwySZLbp4vK3HaoSRm3gb00Idw6sQWq
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_STRIPE_PRICE_ID_MONTHLY=price_1RFj9MGaMPDCTgYKwntD67NT
NEXT_PUBLIC_STRIPE_PRICE_ID_YEARLY=price_1RFj9xGaMPDCTgYKUR9stVUz
STRIPE_WEBHOOK_SECRET=whsec_cX4NsUCXOZWAUL7QOFvJjHjWMZUtEdd0

# BREVO
BREVO_API_KEY=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
BREVO_INVITATION_TEMPLATE_ID=166