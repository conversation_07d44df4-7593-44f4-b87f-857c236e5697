"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Trip } from "@/lib/domains/trip/trip.types"
// import { useRealtimeUserTripStatuses } from "@/lib/domains/user-trip/user-trip.realtime.hooks"
import { useUsersByIds } from "@/lib/domains"
import { useRealtimeUserSubscriptions } from "@/lib/domains/user-subscription/user-subscription.realtime.hooks"
import { Check, X, HelpCircle } from "lucide-react"
import { SubscriberBadge } from "@/components/subscriber-badge"
import { UserDisplay } from "@/components/user-display"
import { User, UserTrip } from "@/lib/domains"

interface AttendeesTabProps {
  trip: Trip
  attendees: (UserTrip & { user: User })[]
  squadMembers: string[]
  currentUserId: string
}

export function AttendeesTab({ trip, attendees, squadMembers, currentUserId }: AttendeesTabProps) {
  // State to track all users (squad members + any additional attendees)
  const [allUsers, setAllUsers] = useState<any[]>([])

  // Get trip statuses with real-time updates
  const { users: squadMembersDetails, loading: squadMembersDetailsLoading } =
    useUsersByIds(squadMembers)
  // Get subscription statuses with real-time updates
  const { subscriptions, loading: subscriptionsLoading } =
    useRealtimeUserSubscriptions(squadMembers)

  // Combine squad members and attendees, removing duplicates
  useEffect(() => {
    const userMap = new Map<string, any>()

    // Add attendees first
    // attendees.forEach((attendee) => {
    //   userMap.set(attendee.userId, attendee)
    // })

    // Add any squad members that might not be in attendees
    squadMembersDetails.forEach((member) => {
      if (!userMap.has(member.uid)) {
        // Find the user in attendees if possible
        const user = attendees.find((a) => a.userId === member.uid)
        if (!user) {
          userMap.set(member.uid, {
            userId: member.uid,
            displayName: member.displayName,
            email: member.email,
            status: "undecided",
          })
        } else {
          userMap.set(user.userId, {
            userId: user.userId,
            displayName: user.user.displayName,
            email: user.user.email,
            status: user.status,
          })
        }
      }
    })
    setAllUsers(Array.from(userMap.values()))
  }, [squadMembersDetails, attendees])

  // Filter all users by status
  const going = allUsers.filter((user) => user.status === "going")
  const notGoing = allUsers.filter((user) => user.status === "not-going")
  const undecided = allUsers.filter(
    (user) => !user.status || user.status === "undecided" || user.status === "pending"
  )

  // Sort the all users list to prioritize going users
  const sortedAllUsers = [...allUsers].sort((a, b) => {
    // Going users first
    if (a.status === "going" && b.status !== "going") return -1
    if (a.status !== "going" && b.status === "going") return 1
    // Then undecided users
    if (a.status === "undecided" && b.status === "not-going") return -1
    if (a.status === "not-going" && b.status === "undecided") return 1
    // Alphabetical by name as a final sort
    return (a.displayName || "").localeCompare(b.displayName || "")
  })

  const renderAttendeeList = (userList: any[]) => (
    <div className="space-y-4 mt-4 max-h-[60vh] overflow-y-auto pr-2">
      {userList.length === 0 ? (
        <p className="text-muted-foreground text-sm">No users in this category</p>
      ) : (
        userList.map((attendee) => (
          <div
            key={attendee.userId}
            className="flex items-center justify-between p-3 rounded-lg border bg-card"
          >
            <div className="flex items-center space-x-3 overflow-hidden">
              <div className="min-w-0 flex-1 overflow-hidden">
                <div className="flex flex-wrap items-center gap-2">
                  <UserDisplay
                    displayName={attendee.displayName}
                    // photoURL={attendee.photoURL || attendee.user.photoURL}
                    showBadge={false} /* We'll show the badge separately */
                  />
                  <div className="flex flex-wrap gap-2">
                    {attendee.userId === trip.leaderId && (
                      <Badge variant="outline" className="text-xs whitespace-nowrap">
                        Trip Leader
                      </Badge>
                    )}
                    {subscriptions?.[attendee.userId] && (
                      <div className="flex items-center bg-amber-50 dark:bg-amber-950 px-2 py-0.5 rounded-full">
                        <SubscriberBadge />
                        <span className="text-xs ml-1 text-amber-600 dark:text-amber-400 font-medium">
                          Pro
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                <p className="text-sm text-muted-foreground truncate ml-10">{attendee.email}</p>
              </div>
            </div>

            {/* Status indicator */}
            <div className="flex items-center shrink-0 ml-2">
              {attendee.status === "going" && (
                <Badge
                  variant="outline"
                  className="bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800 whitespace-nowrap"
                >
                  <Check className="h-3 w-3 mr-1 shrink-0" />
                  <span className="hidden sm:inline">Going</span>
                </Badge>
              )}
              {attendee.status === "not-going" && (
                <Badge
                  variant="outline"
                  className="bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300 dark:border-red-800 whitespace-nowrap"
                >
                  <X className="h-3 w-3 mr-1 shrink-0" />
                  <span className="hidden sm:inline">Not Going</span>
                </Badge>
              )}
              {(attendee.status === "undecided" ||
                !attendee.status ||
                attendee.status === "pending") && (
                <Badge
                  variant="outline"
                  className="bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300 dark:border-yellow-800 whitespace-nowrap"
                >
                  <HelpCircle className="h-3 w-3 mr-1 shrink-0" />
                  <span className="hidden sm:inline">Undecided</span>
                </Badge>
              )}
            </div>
          </div>
        ))
      )}
    </div>
  )

  const loading = squadMembersDetailsLoading || subscriptionsLoading

  return (
    <Card>
      <CardHeader>
        <CardTitle>Attendees</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-pulse flex flex-col items-center">
              <div className="h-12 w-12 bg-muted rounded-full mb-2"></div>
              <div className="h-4 w-32 bg-muted rounded mb-2"></div>
              <div className="h-3 w-24 bg-muted rounded"></div>
            </div>
          </div>
        ) : (
          <Tabs defaultValue="all" className="w-full">
            <div className="overflow-x-auto pb-2 no-scrollbar">
              <TabsList className="w-full md:w-auto inline-flex md:grid md:grid-cols-4">
                <TabsTrigger value="all" className="whitespace-nowrap">
                  All ({allUsers.length})
                </TabsTrigger>
                <TabsTrigger value="going" className="whitespace-nowrap">
                  Going ({going.length})
                </TabsTrigger>
                <TabsTrigger value="undecided" className="whitespace-nowrap">
                  Undecided ({undecided.length})
                </TabsTrigger>
                <TabsTrigger value="not-going" className="whitespace-nowrap">
                  Not Going ({notGoing.length})
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="all">{renderAttendeeList(sortedAllUsers)}</TabsContent>

            <TabsContent value="going">{renderAttendeeList(going)}</TabsContent>

            <TabsContent value="undecided">{renderAttendeeList(undecided)}</TabsContent>

            <TabsContent value="not-going">{renderAttendeeList(notGoing)}</TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  )
}
